﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0-android</TargetFramework>
    <SupportedOSPlatformVersion>23</SupportedOSPlatformVersion>

    <OutputType>Exe</OutputType>
    <ApplicationId>MuAndroid.MuAndroid</ApplicationId>
    <ApplicationVersion>1</ApplicationVersion>
    <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>

    <AndroidManifest>AndroidManifest.xml</AndroidManifest>
    <MonoGamePlatform>Android</MonoGamePlatform>

    <RunAOTCompilation>false</RunAOTCompilation>
    <EnableLLVM>true</EnableLLVM>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MonoGame.Content.Builder.Task" Version="3.8.4" />
    <PackageReference Include="MonoGame.Framework.Android" Version="3.8.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Client.Main\Client.Main.csproj" />
  </ItemGroup>

  <PropertyGroup Condition="'$(Configuration)'=='Release'">
    <AndroidUseInterpreter>true</AndroidUseInterpreter>

    <DebugType>none</DebugType>
    <Optimize>true</Optimize>

    <EmbedAssembliesIntoApk>true</EmbedAssembliesIntoApk>
    <AndroidUseSharedRuntime>false</AndroidUseSharedRuntime>

    <AndroidLinkMode>None</AndroidLinkMode>

    <AndroidCreatePackagePerAbi>false</AndroidCreatePackagePerAbi>
    <AndroidApiLevel>35</AndroidApiLevel>

    <!-- Performance optimizations -->
    <AndroidEnableDebugging>false</AndroidEnableDebugging>
    <AndroidEnableMultiDex>false</AndroidEnableMultiDex>
    <AndroidPackageFormat>apk</AndroidPackageFormat>
    <AndroidUseAapt2>true</AndroidUseAapt2>
  </PropertyGroup>

  <Target Name="RestoreDotnetTools" BeforeTargets="Restore">
    <Message Text="Restoring dotnet tools" Importance="High" />
    <Exec Command="dotnet tool restore" />
  </Target>

</Project>