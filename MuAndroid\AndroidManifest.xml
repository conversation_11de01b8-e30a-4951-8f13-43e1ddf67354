﻿<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          package="MuAndroid.MuAndroid"
          android:versionCode="1"
          android:versionName="1.0">
  <uses-sdk android:minSdkVersion="23" android:targetSdkVersion="35" />
  <uses-feature android:glEsVersion="0x00020000" android:required="true" />
  
  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
	<application android:label="MUV1.0.4"
               android:icon="@drawable/Icon"
               android:usesCleartextTraffic="true"
               android:hardwareAccelerated="true"
               android:largeHeap="true">
  </application>
</manifest>
