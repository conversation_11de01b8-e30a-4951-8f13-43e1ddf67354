using Microsoft.Xna.Framework;

namespace Client.Main.Configuration
{
    /// <summary>
    /// Configuration class for rendering settings that can be easily adjusted
    /// </summary>
    public static class RenderingConfig
    {
        // Lighting Configuration
        public static float AmbientLightIntensity { get; set; } = 0.85f;
        public static Vector3 DirectionalLight { get; set; } = new Vector3(0.3f, -0.7f, 0.8f);
        public static float MinimumLuminosity { get; set; } = 0.3f;
        public static float LuminosityOffset { get; set; } = 0.7f;
        
        // Performance Configuration
        public static int TextureLoadBatchSize { get; set; } = 6;
        public static int ObjectLoadBatchSize { get; set; } = 10;
        public static int TextureLoadDelay { get; set; } = 10; // milliseconds
        public static int ObjectLoadDelay { get; set; } = 5; // milliseconds
        
        // Fallback Configuration
        public static bool UseFallbackTextures { get; set; } = true;
        public static bool UseProgressiveLoading { get; set; } = true;
        public static bool UseParallelLoading { get; set; } = true;
        
        /// <summary>
        /// Apply optimized settings for better performance
        /// </summary>
        public static void ApplyPerformanceOptimizations()
        {
            AmbientLightIntensity = 0.85f;
            DirectionalLight = new Vector3(0.3f, -0.7f, 0.8f);
            MinimumLuminosity = 0.3f;
            LuminosityOffset = 0.7f;
            
            TextureLoadBatchSize = 6;
            ObjectLoadBatchSize = 10;
            TextureLoadDelay = 10;
            ObjectLoadDelay = 5;
            
            UseFallbackTextures = true;
            UseProgressiveLoading = true;
            UseParallelLoading = true;
        }
        
        /// <summary>
        /// Apply settings for better visual quality (may impact performance)
        /// </summary>
        public static void ApplyQualityOptimizations()
        {
            AmbientLightIntensity = 0.9f;
            DirectionalLight = new Vector3(0.2f, -0.8f, 0.9f);
            MinimumLuminosity = 0.4f;
            LuminosityOffset = 0.8f;
            
            TextureLoadBatchSize = 12;
            ObjectLoadBatchSize = 20;
            TextureLoadDelay = 5;
            ObjectLoadDelay = 2;
            
            UseFallbackTextures = true;
            UseProgressiveLoading = false;
            UseParallelLoading = true;
        }
    }
}
