using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Input;
using Microsoft.Xna.Framework.Input.Touch;
using System;

namespace Client.Main.Input
{
    /// <summary>
    /// Unified input manager that handles both touch and mouse input,
    /// with platform-specific optimizations for mobile devices.
    /// </summary>
    public static class InputManager
    {
        private static bool _isTouchPrimary = false;
        private static DateTime _lastTouchTime = DateTime.MinValue;
        private static readonly TimeSpan TouchCooldown = TimeSpan.FromMilliseconds(50);

        /// <summary>
        /// Gets whether touch input is currently being used as the primary input method.
        /// </summary>
        public static bool IsTouchPrimary => _isTouchPrimary;

        /// <summary>
        /// Gets whether the current platform should prioritize touch input.
        /// </summary>
        public static bool ShouldPrioritizeTouch
        {
            get
            {
#if ANDROID || IOS
                return true;
#else
                return false;
#endif
            }
        }

        /// <summary>
        /// Updates the input state and determines the primary input method.
        /// </summary>
        /// <param name="touchState">Current touch state</param>
        /// <param name="mouseState">Current mouse state</param>
        /// <param name="prevTouchState">Previous touch state</param>
        /// <param name="prevMouseState">Previous mouse state</param>
        public static void UpdateInputState(TouchCollection touchState, MouseState mouseState,
            TouchCollection prevTouchState, MouseState prevMouseState)
        {
#if ANDROID
            // On Android, always prioritize touch if available
            _isTouchPrimary = touchState.Count > 0;

            // Debug logging for Android
            if (touchState.Count > 0)
            {
                var touch = touchState[0];
                System.Diagnostics.Debug.WriteLine($"[InputManager] Touch detected: {touch.Position}, State: {touch.State}");
            }
#else
            var now = DateTime.UtcNow;

            // Detect touch activity
            bool hasTouchActivity = touchState.Count > 0 ||
                                   (prevTouchState.Count > 0 && touchState.Count == 0);

            // Detect mouse activity (excluding synthetic mouse events from touch)
            bool hasMouseActivity = mouseState.Position != prevMouseState.Position ||
                                   mouseState.LeftButton != prevMouseState.LeftButton ||
                                   mouseState.RightButton != prevMouseState.RightButton ||
                                   mouseState.ScrollWheelValue != prevMouseState.ScrollWheelValue;

            // Update touch primary status
            if (ShouldPrioritizeTouch)
            {
                if (hasTouchActivity)
                {
                    _isTouchPrimary = true;
                    _lastTouchTime = now;
                }
                else if (hasMouseActivity && (now - _lastTouchTime) > TouchCooldown)
                {
                    // Only switch to mouse if no touch activity for a while
                    _isTouchPrimary = false;
                }
            }
            else
            {
                // On desktop, prefer mouse unless touch is explicitly used
                _isTouchPrimary = hasTouchActivity && !hasMouseActivity;
            }
#endif
        }

        /// <summary>
        /// Converts touch input to a mouse state for compatibility with existing mouse-based code.
        /// </summary>
        /// <param name="touchState">Current touch state</param>
        /// <param name="prevTouchState">Previous touch state</param>
        /// <param name="fallbackMouseState">Mouse state to use as fallback</param>
        /// <returns>A mouse state representing the touch input</returns>
        public static MouseState ConvertTouchToMouseState(TouchCollection touchState,
            TouchCollection prevTouchState, MouseState fallbackMouseState)
        {
            if (touchState.Count == 0)
            {
                // No touch, return fallback mouse state
                return fallbackMouseState;
            }

            var primaryTouch = touchState[0];

            // Touch coordinates are already in screen pixels, no need to convert
            var touchPosition = new Point(
                (int)primaryTouch.Position.X,
                (int)primaryTouch.Position.Y
            );

            // Determine button state based on touch state
            ButtonState leftButton = ButtonState.Released;

            switch (primaryTouch.State)
            {
                case TouchLocationState.Pressed:
                case TouchLocationState.Moved:
                    leftButton = ButtonState.Pressed;
                    break;
                case TouchLocationState.Released:
                    // Check if this is a fresh release (was pressed in previous frame)
                    if (prevTouchState.Count > 0)
                    {
                        var prevTouch = prevTouchState[0];
                        if (prevTouch.State == TouchLocationState.Pressed ||
                            prevTouch.State == TouchLocationState.Moved)
                        {
                            leftButton = ButtonState.Released;
                        }
                    }
                    break;
            }

            var result = new MouseState(
                touchPosition.X,
                touchPosition.Y,
                fallbackMouseState.ScrollWheelValue, // Preserve scroll wheel
                leftButton,
                ButtonState.Released, // Middle button
                ButtonState.Released, // Right button
                ButtonState.Released, // X button 1
                ButtonState.Released  // X button 2
            );

#if ANDROID
            // Debug logging for Android
            System.Diagnostics.Debug.WriteLine($"[InputManager] ConvertTouch: Pos={touchPosition}, Button={leftButton}, TouchState={primaryTouch.State}");
#endif

            return result;
        }

        /// <summary>
        /// Gets the effective mouse state, considering touch input conversion.
        /// </summary>
        /// <param name="mouseState">Raw mouse state</param>
        /// <param name="touchState">Current touch state</param>
        /// <param name="prevTouchState">Previous touch state</param>
        /// <returns>The effective mouse state to use</returns>
        public static MouseState GetEffectiveMouseState(MouseState mouseState, 
            TouchCollection touchState, TouchCollection prevTouchState)
        {
            if (_isTouchPrimary && touchState.Count > 0)
            {
                return ConvertTouchToMouseState(touchState, prevTouchState, mouseState);
            }

            return mouseState;
        }

        /// <summary>
        /// Checks if a click/tap gesture has occurred.
        /// </summary>
        /// <param name="currentState">Current mouse/touch state</param>
        /// <param name="previousState">Previous mouse/touch state</param>
        /// <param name="touchState">Current touch state</param>
        /// <param name="prevTouchState">Previous touch state</param>
        /// <returns>True if a click/tap gesture was detected</returns>
        public static bool IsClickGesture(MouseState currentState, MouseState previousState,
            TouchCollection touchState, TouchCollection prevTouchState)
        {
            if (_isTouchPrimary)
            {
                // For touch, detect when touch starts (press) - immediate response
                bool touchStarted = touchState.Count > 0 && prevTouchState.Count == 0 &&
                                   touchState[0].State == TouchLocationState.Pressed;

                // Also detect tap completion for UI consistency
                bool touchTapped = prevTouchState.Count > 0 && touchState.Count == 0 &&
                                  (prevTouchState[0].State == TouchLocationState.Pressed ||
                                   prevTouchState[0].State == TouchLocationState.Moved);

#if ANDROID
                if (touchStarted || touchTapped)
                {
                    System.Diagnostics.Debug.WriteLine($"[InputManager] Touch gesture: Started={touchStarted}, Tapped={touchTapped}");
                }
#endif

                return touchStarted || touchTapped;
            }
            else
            {
                // For mouse, detect click
                return currentState.LeftButton == ButtonState.Pressed &&
                       previousState.LeftButton == ButtonState.Released;
            }
        }

        /// <summary>
        /// Gets the current input position (mouse or primary touch).
        /// </summary>
        /// <param name="mouseState">Current mouse state</param>
        /// <param name="touchState">Current touch state</param>
        /// <returns>The current input position</returns>
        public static Point GetInputPosition(MouseState mouseState, TouchCollection touchState)
        {
            if (_isTouchPrimary && touchState.Count > 0)
            {
                var touch = touchState[0];

#if ANDROID
                // Debug logging for Android touch coordinates
                System.Diagnostics.Debug.WriteLine($"[InputManager] Touch position: {touch.Position.X}, {touch.Position.Y}");
#endif

                // Touch coordinates are already in screen pixels, no need to convert
                return new Point(
                    (int)touch.Position.X,
                    (int)touch.Position.Y
                );
            }

            return mouseState.Position;
        }
    }
}
