﻿<?xml version="1.0" encoding="utf-8"?>
<!--
This file contains an xml description of a font, and will be read by the XNA
Framework Content Pipeline. Follow the comments to customize the appearance
of the font in your game, and to change the characters which are available to draw
with.
-->
<XnaContent xmlns:Graphics="Microsoft.Xna.Framework.Content.Pipeline.Graphics">
  <Asset Type="Graphics:FontDescription">

    <!--
    Modify this string to change the font that will be imported.
    -->
    <FontName>Arial</FontName>

    <!--
    Size is a float value, measured in points. Modify this value to change
    the size of the font.
    -->
    <Size>25</Size>

    <!--
    Spacing is a float value, measured in pixels. Modify this value to change
    the amount of spacing in between characters.
    -->
    <Spacing>0</Spacing>

    <!--
    UseKerning controls the layout of the font. If this value is true, kerning information
    will be used when placing characters.
    -->
    <UseKerning>true</UseKerning>

    <!--
    Style controls the style of the font. Valid entries are "Regular", "Bold", "Italic",
    and "Bold, Italic", and are case sensitive.
    -->
    <Style>Regular</Style>

    <DefaultCharacter>?</DefaultCharacter>

    <CharacterRegions>
      <!-- ASCII Standard -->
      <CharacterRegion>
        <Start> </Start>
        <End>~</End>
      </CharacterRegion>
      <!-- Latin-1 Supplement -->
      <CharacterRegion>
        <Start>À</Start> <!-- À (U+00C0) -->
        <End>ÿ</End>   <!-- ÿ (U+00FF) -->
      </CharacterRegion>
      <!-- Latin Extended-A -->
      <CharacterRegion>
        <Start>Ā</Start> <!-- Ā (U+0100) -->
        <End>ſ</End>   <!-- ſ (U+017F) -->
      </CharacterRegion>
    </CharacterRegions>
  </Asset>
</XnaContent>