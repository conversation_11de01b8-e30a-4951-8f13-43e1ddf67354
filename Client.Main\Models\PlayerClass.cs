﻿namespace Client.Main.Models
{
    public enum PlayerClass
    {/* 1st                  2nd                    3rd                     4th                    */
        DarkWizard     =  1, SoulMaster      = 201, GrandMaster      = 301, SoulWizard         = 401,
        DarkKnight     =  2, BladeKnight     = 202, BladeMaster      = 302, DragonKnight       = 402, 
        FairyElf       =  3, MuseElf         = 203, HighElf          = 303, NobleElf           = 403,
        MagicGladiator =  4,                        DuelMaster       = 304, MagicKnight        = 404,
        DarkLord       =  5,                        <PERSON><PERSON><PERSON><PERSON>      = 305, EmpireLord         = 405,
        Summoner       =  6, BloodySummoner  = 206, DimensionMaster  = 306, DimensionSummoner  = 406,
        RageFighter    =  7,                        FistMaster       = 307, FistBlazer         = 407,
        GlowLancer     =  8,                        MirageLancer     = 308, ShiningLancer      = 408,
        RuneMage       =  9, RuneSpellMaster = 209, GradRuneMaster   = 309, MajesticRuneWizard = 409,
        Slayer         = 10, RoyalSlayer     = 210, MasterSlayer     = 310, Slaughterer        = 410,
        GunCrusher     = 11, GunBreaker      = 211, MasterGunBreaker = 311, HeistGunCrasher    = 411,
        WhiteWizard    = 12, LightMaster     = 212, ShineWizard      = 312, <PERSON>Master        = 412,
        <PERSON><PERSON>           = 13, <PERSON><PERSON><PERSON><PERSON>          = 213, <PERSON><PERSON>age         = 313, <PERSON>sticMage         = 413,
        <PERSON><PERSON>Knight = 14, MirageKnight    = 214, <PERSON>lusionMaster   = 314, MysticKnight       = 414,
        Alchemist      = 15, AlchemicMaster  = 215, AlchemicForce    = 315, Creator            = 415,
    }
}
