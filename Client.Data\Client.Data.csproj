﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net9.0;net9.0-windows;net9.0-android;net9.0-ios</TargetFrameworks>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <PublishAot>true</PublishAot>
    <IncludeMobileTargets Condition="'$(IncludeMobileTargets)' == ''">false</IncludeMobileTargets>
  </PropertyGroup>

  <Choose>
    <When Condition="'$(IncludeMobileTargets)' == 'true'">
      <PropertyGroup>
        <TargetFrameworks>net9.0;net9.0-windows;net9.0-android;net9.0-ios</TargetFrameworks>
      </PropertyGroup>
    </When>
    <Otherwise>
      <PropertyGroup>
        <TargetFrameworks>net9.0;net9.0-windows</TargetFrameworks>
      </PropertyGroup>
    </Otherwise>
  </Choose>

  <ItemGroup>
    <PackageReference Include="BCnEncoder.Net" Version="2.2.0" />
    <PackageReference Include="BouncyCastle.NetCore" Version="2.2.1" />
    <PackageReference Include="cryptopp" Version="5.6.5.4" />
    <PackageReference Include="Delizious.Ini" Version="1.17.0" />
    <PackageReference Include="LEA.NET" Version="1.0.1" />
    <PackageReference Include="sixlabors.imagesharp" Version="3.1.7" />
  </ItemGroup>

</Project>
