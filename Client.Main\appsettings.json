{"Logging": {"LogLevel": {"Default": "Trace", "Microsoft.Hosting.Lifetime": "Trace", "Client.Main.Controls.UI.Game.CharacterInfoWindowControl": "Trace", "Client.Main.Networking.NetworkManager": "Trace", "Client.Main.Networking.PacketHandling.PacketRouter": "Trace", "Client.Main.Networking.PacketHandling.Handlers.ChatMessageHandler": "Trace", "Client.Main.Networking.PacketHandling.Handlers.CharacterDataHandler": "Trace", "Client.Main.Networking.PacketHandling.Handlers.MiscGamePacketHandler": "Trace", "Client.Main.Networking.PacketHandling.Handlers.ScopeHandler": "Trace", "Client.Main.Core.Client.ScopeManager": "Trace", "Client.Main.Objects.DroppedItemObject": "Trace", "Client.Main.Networking.ConnectionManager": "Trace", "Client.Main.Networking.Services.LoginService": "Trace", "Client.Main.Networking.Services.CharacterService": "Trace", "Client.Main.Networking.PacketHandling.Handlers.ConnectServerHandler": "Trace", "Client.Main.Networking.PacketHandling.Handlers.InventoryHandler": "Trace", "Client.Main.MuGame": "Trace", "Client.Main.Scenes.GameScene": "Trace", "Client.Main.Content.TextureLoader": "Trace", "Client.Main.Controls.UI.TextureControl": "Trace"}, "SimpleConsole": {"TimestampFormat": "HH:mm:ss.fff ", "SingleLine": true, "IncludeScopes": false}}, "MuOnlineSettings": {"ConnectServerHost": "**************", "ConnectServerPort": 44405, "ProtocolVersion": "Season6", "ClientVersion": "1.04d", "ClientSerial": "0123456789ABCDEF", "DirectionMap": {"0": 7, "1": 6, "2": 5, "3": 4, "4": 3, "5": 2, "6": 1, "7": 0}, "PacketLogging": {"ShowWeather": true, "ShowDamage": true}}}