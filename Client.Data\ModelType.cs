﻿namespace Client.Data
{
    public enum ModelType : ushort
    {
        Tree01 = 0,
        Tree02 = 1,
        Tree03 = 2,
        Tree0<PERSON> = 3,
        <PERSON>0<PERSON> = 4,
        Tree06 = 5,
        Tree07 = 6,
        Tree08 = 7,
        Tree09 = 8,
        <PERSON>10 = 9,
        <PERSON>11 = 10,
        <PERSON>12 = 11,
        <PERSON><PERSON> = 12,

        <PERSON><PERSON> = 20,
        <PERSON>02 = 21,
        <PERSON>03 = 22,
        <PERSON>0<PERSON> = 23,
        <PERSON>05 = 24,
        Grass06 = 25,
        <PERSON>07 = 26,
        Grass08 = 27,

        <PERSON><PERSON> = 30,
        Stone02 = 31,
        <PERSON>0<PERSON> = 32,
        <PERSON>0<PERSON> = 33,
        Stone0<PERSON> = 34,

        <PERSON>Statue01 = 40,
        <PERSON>Statue02 = 41,
        <PERSON>Statue03 = 42,
        SteelStatue = 43,
        Tomb01 = 44,
        Tomb02 = 45,
        Tomb03 = 46,

        Unknown48 = 48,

        <PERSON>L<PERSON>01 = 50,
        <PERSON>Light02 = 51,
        <PERSON><PERSON> = 52,

        <PERSON><PERSON>onGate = 55,
        MerchantAnimal01 = 56,
        <PERSON>Animal02 = 57,
        TreasureDrum = 58,
        TreasureChest = 59,
        <PERSON> = 60,
        SteelWall01 = 65,
        BonFire02 = 66,
        <PERSON><PERSON>all03 = 67,
        <PERSON><PERSON><PERSON> = 68,
        <PERSON><PERSON><PERSON><PERSON> = 69,
        <PERSON><PERSON><PERSON>02 = 70,
        <PERSON><PERSON><PERSON>03 = 71,
        <PERSON><PERSON><PERSON>04 = 72,
        <PERSON><PERSON><PERSON>0<PERSON> = 73,
        <PERSON><PERSON><PERSON>06 = 74,
        <PERSON><PERSON><PERSON><PERSON> = 75,
        <PERSON><PERSON><PERSON>02 = 76,
        <PERSON><PERSON><PERSON>03 = 77,
        <PERSON>Wall04 = 78,
        Unknown79 = 79,
        Bridge = 80,
        Fence01 = 81,
        Fence02 = 82,
        Fence03 = 83,
        Fence04 = 84,
        BridgeStone = 85,
        SlidingDoors = 86,
        SteelWall04 = 88,

        StreetLight = 90,
        Cannon01 = 91,
        Cannon02 = 92,
        Cannon03 = 93,
        Unknown94 = 94,
        Curtain = 95,
        Sign01 = 96,
        Sign02 = 97,
        Carriage01 = 98,
        Carriage02 = 99,
        Carriage03 = 100,
        Carriage04 = 101,
        Straw01 = 102,
        Straw02 = 103,

        WaterSpout = 105,
        Well01 = 106,
        Well02 = 107,
        Well03 = 108,
        Well04 = 109,
        Hanging = 110,
        Stair = 111,

        Unknown112 = 112,
        Unknown113 = 113,

        House01 = 115,
        House02 = 116,
        House03 = 117,
        House04 = 118,
        House05 = 119,
        Tent = 120,
        HouseWall01 = 121,
        HouseWall02 = 122,
        HouseWall03 = 123,
        HouseWall04 = 124,
        HouseWall05 = 125,
        HouseWall06 = 126,
        HouseEtc01 = 127,
        HouseEtc02 = 128,
        HouseEtc03 = 129,
        Light01 = 130,
        Light02 = 131,
        Light03 = 132,
        PoseBox = 133,
        Unknown134 = 134,
        Unknown135 = 135,
        Unknown136 = 136,
        Unknown137 = 137,
        Unknown138 = 138,
        Unknown139 = 139,
        Furniture01 = 140,
        Furniture02 = 141,
        Furniture03 = 142,
        Furniture04 = 143,
        Furniture05 = 144,
        Furniture06 = 145,
        Furniture07 = 146,
        Unknown147 = 147,

        Candle = 150,
        Beer01 = 151,
        Beer02 = 152,
        Beer03 = 153,
        HeroChair = 154,
        HeroGuard = 155,
        MurdererDog = 156,

        Unknown157 = 157,

        Unknown159 = 159,

        Logo = 160,
        WaveByShip = 161,
        MuGame = 162,
        LogoSun = 163,
        Card = 164,

        Face01 = 168,
        Face02 = 169,
        Face03 = 170,
        Face04 = 171,
        Face05 = 172,
        Face06 = 173,
        Face07 = 174,

        ITEM_GROUP_SWORD = 0 * 512,
        ITEM_GROUP_AXE = 1 * 512,
        ITEM_GROUP_MACE_SCEPTER = 2 * 512,
        ITEM_GROUP_SPEAR = 3 * 512,
        ITEM_GROUP_BOW_CROSSBOW = 4 * 512,
        ITEM_GROUP_STAFF = 5 * 512,
        ITEM_GROUP_SHIELD = 6 * 512,
        ITEM_GROUP_HELM = 7 * 512,
        ITEM_GROUP_ARMOR = 8 * 512,
        ITEM_GROUP_PANT = 9 * 512,
        ITEM_GROUP_GLOVE = 10 * 512,
        ITEM_GROUP_BOOT = 11 * 512,
        ITEM_GROUP_WING_ORB_ETC = 12 * 512,
        ITEM_GROUP_PET_RING_PENDANT = 13 * 512,
        ITEM_GROUP_POTION_JEWEL_QUEST = 14 * 512, 
        ITEM_GROUP_SCROLL = 15 * 512,

        Wings_of_Elf = ITEM_GROUP_WING_ORB_ETC + 0,
        Wings_of_Heaven = ITEM_GROUP_WING_ORB_ETC + 1,
        Wings_of_Satan = ITEM_GROUP_WING_ORB_ETC + 2,
        Wings_of_Spirits = ITEM_GROUP_WING_ORB_ETC + 3,
        Wings_of_Soul = ITEM_GROUP_WING_ORB_ETC + 4,
        Wings_of_Dragon = ITEM_GROUP_WING_ORB_ETC + 5,
        Wings_of_Darkness = ITEM_GROUP_WING_ORB_ETC + 6,
        Wing_of_Storm = ITEM_GROUP_WING_ORB_ETC + 36,
        Wing_of_Eternal = ITEM_GROUP_WING_ORB_ETC + 37,
        Wing_of_Illusion = ITEM_GROUP_WING_ORB_ETC + 38,
        Wing_of_Ruin = ITEM_GROUP_WING_ORB_ETC + 39,
        Cape_of_Emperor = ITEM_GROUP_WING_ORB_ETC + 40,
        Wing_of_Curse = ITEM_GROUP_WING_ORB_ETC + 41,
        Wings_of_Despair = ITEM_GROUP_WING_ORB_ETC + 42,
        Wing_of_Dimension = ITEM_GROUP_WING_ORB_ETC + 43,
        Cape_of_Fighter = ITEM_GROUP_WING_ORB_ETC + 49,
        Cape_of_Overrule = ITEM_GROUP_WING_ORB_ETC + 50,

        Berdysh = ITEM_GROUP_SPEAR + 7
    }
}
