<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>

    <RollForward>Major</RollForward>
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>

    <PublishTrimmed>false</PublishTrimmed>
    <PublishReadyToRun>false</PublishReadyToRun>
    <TieredCompilation>true</TieredCompilation>

    <ApplicationManifest>app.manifest</ApplicationManifest>
    <ApplicationIcon>Icon.ico</ApplicationIcon>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <Optimize>true</Optimize>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MonoGame.Framework.DesktopGL" Version="3.8.4" />
    <PackageReference Include="MonoGame.Content.Builder.Task" Version="3.8.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Client.Main\Client.Main.csproj" />
  </ItemGroup>

  <ItemGroup>
    <TrimmerRootAssembly Include="MonoGame.Framework" />
    <TrimmerRootAssembly Include="mscorlib" />
  </ItemGroup>

  <Target Name="RestoreDotnetTools" BeforeTargets="Restore">
    <Message Text="Restoring dotnet tools" Importance="High" />
    <Exec Command="dotnet tool restore" />
  </Target>

</Project>