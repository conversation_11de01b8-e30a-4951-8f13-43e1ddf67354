﻿using Client.Data;
using Client.Main.Content;
using Microsoft.Xna.Framework.Graphics;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Client.Main.Objects.Worlds.Lorencia
{
    public class ShipObject : ModelObject
    {
        public ShipObject()
        {
            LightEnabled = true;
        }

        public override async Task LoadContent()
        {
            Model = await BMDLoader.Instance.Prepare($"Object1/Ship01.bmd");
            await base.LoadContent();
        }
    }
}
