using Client.Main.Content;
using System.Threading.Tasks;

namespace Client.Main.Objects.NPCS
{
    [NpcIn<PERSON>(579, "<PERSON>")]
    public class David : NPCObject
    {
        public override async Task Load()
        {
            Model = await BMDLoader.Instance.Prepare($"NPC/LuckyItem_Npc.bmd");
            await base.Load();
        }
        protected override void HandleClick()
        {
        }
    }
}
