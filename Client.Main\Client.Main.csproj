<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFrameworks>net9.0;net9.0-windows;net9.0-android</TargetFrameworks>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <ItemGroup Condition="'$(TargetFramework)'=='net9.0-android'">
    <PackageReference Include="MonoGame.Framework.Android" Version="3.8.4" />
  </ItemGroup>

  <ItemGroup Condition="'$(TargetFramework)'=='net9.0-windows'">
    <PackageReference Include="MonoGame.Framework.DesktopGL" Version="3.8.4">
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup Condition="'$(TargetFramework)'=='net9.0'">
    <PackageReference Include="MonoGame.Framework.DesktopGL" Version="3.8.4">
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="MUnique.OpenMU.Network" Version="0.9.6" />
    <PackageReference Include="MUnique.OpenMU.Network.Packets" Version="0.9.6" />
    <PackageReference Include="NLayer" Version="1.16.0" />
    <PackageReference Include="Pipelines.Sockets.Unofficial" Version="2.2.16" />

    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.5" />
    <PackageReference Include="System.IO.Pipelines" Version="9.0.5" />
  </ItemGroup>

  <ItemGroup>
    <TrimmerRootAssembly Include="MonoGame.Framework" />
    <TrimmerRootAssembly Include="mscorlib" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Controls\UI\Game\Layouts\*.json" />
  </ItemGroup>

  <ItemGroup Condition="'$(TargetFramework)'!='net9.0-android'">
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup Condition="'$(TargetFramework)'=='net9.0-android'">
    <AndroidAsset Include="appsettings.json" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Client.Data\Client.Data.csproj" />
  </ItemGroup>

</Project>